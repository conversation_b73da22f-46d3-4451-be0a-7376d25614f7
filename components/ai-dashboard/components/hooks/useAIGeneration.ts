"use client";

import { useState, useEffect } from "react";
import { toast } from "sonner";
import { useTranslations } from "next-intl";
import type { AIModel, GenerationResult, GenerationOptions, CostEstimate } from "../types";

export function useAIGeneration(
  modelType?: string,
  onResultChange?: (result: GenerationResult | null) => void,
  onGeneratingChange?: (isGenerating: boolean) => void
) {
  const t = useTranslations("ai-dashboard");
  const [selectedModel, setSelectedModel] = useState<AIModel | null>(null);
  const [allModels, setAllModels] = useState<AIModel[]>([]);
  const [models, setModels] = useState<AIModel[]>([]);
  const [modelsLoading, setModelsLoading] = useState(true);
  const [modelsError, setModelsError] = useState<string | null>(null);
  const [prompt, setPrompt] = useState("");
  const [options, setOptions] = useState<GenerationOptions>({
    size: '1:1',
    aspectRatio: '1:1',
    variants: 1,
    temperature: 0.7,
    max_tokens: 1000,
    cdn: 'global',
    uploadedImages: [],
    referenceImages: []
  });
  const [loading, setLoading] = useState(false);
  const [costEstimate, setCostEstimate] = useState<CostEstimate | null>(null);
  const [userCredits, setUserCredits] = useState<number>(0);

  useEffect(() => {
    fetchUserCredits();
    fetchAllModels();
  }, []);

  useEffect(() => {
    filterModels();
    if (selectedModel && modelType && selectedModel.model_type !== modelType) {
      setSelectedModel(null);
    }
  }, [modelType, allModels]);

  useEffect(() => {
    if (selectedModel && prompt) {
      estimateCost();
    }
  }, [selectedModel, prompt, options]);

  useEffect(() => {
    if (selectedModel) {
      const newOptions = { ...options };

      if (selectedModel.model_type === 'image') {
        if (!newOptions.size && !newOptions.aspectRatio) {
          newOptions.size = '1:1';
          newOptions.aspectRatio = '1:1';
        }
        if (!newOptions.variants) {
          newOptions.variants = 1;
        }
      } else if (selectedModel.model_type === 'text' || selectedModel.model_type === 'multimodal') {
        if (!newOptions.max_tokens) {
          newOptions.max_tokens = 1000;
        }
        if (!newOptions.temperature) {
          newOptions.temperature = 0.7;
        }
      }

      if (!newOptions.cdn) {
        newOptions.cdn = 'global';
      }

      setOptions(newOptions);
    }
  }, [selectedModel]);

  const fetchAllModels = async () => {
    try {
      setModelsLoading(true);
      setModelsError(null);

      const response = await fetch('/api/ai/models');
      const data = await response.json();

      if (data.code === 0) {
        const models = data.data.models;
        setAllModels(models);
      } else {
        setModelsError(data.msg || 'Failed to fetch models');
      }
    } catch (err) {
      setModelsError('Network error');
    } finally {
      setModelsLoading(false);
    }
  };

  const filterModels = () => {
    const filteredModels = modelType
      ? allModels.filter((model: AIModel) => model.model_type === modelType)
      : allModels;

    setModels(filteredModels);
  };

  const fetchUserCredits = async () => {
    try {
      const response = await fetch('/api/get-user-info', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' }
      });
      const data = await response.json();
      if (data.code === 0) {
        setUserCredits(data.data.credits?.left_credits || 0);
      }
    } catch (error) {
      console.error('Failed to fetch user credits:', error);
    }
  };

  const estimateCost = async () => {
    if (!selectedModel || !prompt) return;

    try {
      const response = await fetch('/api/ai/estimate-cost', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          model: selectedModel.model_id,
          type: selectedModel.model_type,
          prompt,
          options
        })
      });
      const data = await response.json();
      if (data.code === 0) {
        setCostEstimate(data.data);
      }
    } catch (error) {
      console.error('Failed to estimate cost:', error);
    }
  };

  const handleGenerate = async () => {
    if (!selectedModel || !prompt.trim()) {
      toast.error(t("errors.invalid_input"));
      return;
    }

    if (costEstimate && !costEstimate.user_credits.can_afford) {
      toast.error(t("errors.insufficient_credits"));
      return;
    }

    setLoading(true);
    onGeneratingChange?.(true);
    onResultChange?.(null);

    try {
      const response = await fetch('/api/ai/generate', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          model: selectedModel.model_id,
          type: selectedModel.model_type,
          prompt,
          options
        })
      });

      const data = await response.json();

      if (data.code === 0) {
        if (data.data.status === 'pending' || data.data.status === 'running') {
          onResultChange?.(data.data);
          pollResult(data.data.id);
        } else {
          onResultChange?.(data.data);
          toast.success(t("status.success"));
          fetchUserCredits();
          onGeneratingChange?.(false);
        }
      } else {
        toast.error(data.msg || t("errors.generation_failed", { detail: "Unknown error" }));
        onGeneratingChange?.(false);
      }
    } catch (error) {
      toast.error(t("errors.network_error"));
      onGeneratingChange?.(false);
    } finally {
      setLoading(false);
    }
  };

  const pollResult = async (requestId: string) => {
    const maxAttempts = 60;
    let attempts = 0;

    const poll = async () => {
      try {
        const response = await fetch('/api/ai/result', {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({ request_id: requestId })
        });

        const data = await response.json();
        
        if (data.code === 0) {
          onResultChange?.(data.data);

          if (data.data.status === 'success') {
            toast.success(t("status.success"));
            fetchUserCredits();
            onGeneratingChange?.(false);
            return;
          } else if (data.data.status === 'failed') {
            toast.error(t("errors.generation_failed", { detail: data.data.error?.detail || "Unknown error" }));
            onGeneratingChange?.(false);
            return;
          }
        }

        attempts++;
        if (attempts < maxAttempts) {
          setTimeout(poll, 5000);
        } else {
          toast.error(t("errors.network_error"));
        }
      } catch (error) {
        console.error('Polling error:', error);
        attempts++;
        if (attempts < maxAttempts) {
          setTimeout(poll, 5000);
        }
      }
    };

    poll();
  };

  const handleModelSelect = (modelId: string) => {
    const model = models.find(m => m.model_id === modelId);
    if (model) {
      setSelectedModel(model);
    }
  };

  return {
    selectedModel,
    models,
    modelsLoading,
    modelsError,
    prompt,
    setPrompt,
    options,
    setOptions,
    loading,
    costEstimate,
    userCredits,
    handleGenerate,
    handleModelSelect,
    fetchUserCredits
  };
}
