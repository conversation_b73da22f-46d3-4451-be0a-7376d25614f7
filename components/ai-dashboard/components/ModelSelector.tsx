"use client";

import { Label } from "@/components/ui/label";
import { Loader2 } from "lucide-react";
import {
  RichSelect,
  RichSelectContent,
  RichSelectItem,
  RichSelectTrigger,
  RichSelectValue,
  type RichSelectOption
} from "@/components/ui/rich-select";
import { useTranslations } from "next-intl";
import type { AIModel } from "./types";
import { MODEL_TYPE_ICONS } from "./types";

interface ModelSelectorProps {
  selectedModel: AIModel | null;
  models: AIModel[];
  modelsLoading: boolean;
  modelsError: string | null;
  onModelSelect: (modelId: string) => void;
}

export function ModelSelector({
  selectedModel,
  models,
  modelsLoading,
  modelsError,
  onModelSelect
}: ModelSelectorProps) {
  const t = useTranslations("ai-dashboard.models");
  const convertModelToOption = (model: AIModel): RichSelectOption => {
    const iconElement = model.icon ? (
      <img
        src={model.icon}
        alt={model.model_name}
        className="w-8 h-8 rounded-full object-contain"
      />
    ) : (
      (() => {
        const Icon = MODEL_TYPE_ICONS[model.model_type as keyof typeof MODEL_TYPE_ICONS] || MODEL_TYPE_ICONS.text;
        return <Icon className="w-8 h-8 p-1.5 rounded-full bg-gradient-to-r from-primary to-accent text-primary-foreground" />;
      })()
    );

    return {
      value: model.model_id,
      label: model.model_name,
      description: model.description || `${model.credits_per_unit} credits/${model.unit_type} `,
      icon: iconElement,
    };
  };

  const getAllModelOptions = (): RichSelectOption[] => {
    return models.map(convertModelToOption);
  };

  return (
    <div className="px-3 py-2 bg-gradient-to-r from-muted/20 to-muted/10 rounded-xl border border-border/30 w-full max-w-full overflow-x-hidden">
      <Label htmlFor="model" className="text-sm font-medium text-foreground">{t("model_selector")}</Label>
      {modelsLoading ? (
        <div className="flex items-center justify-center p-4 bg-gradient-to-r from-accent/10 to-primary/10 rounded-md border border-border/20 mt-2">
          <Loader2 className="w-4 h-4 animate-spin mr-2" />
          <span className="text-sm">{t("loading")}</span>
        </div>
      ) : modelsError ? (
        <div className="p-4 bg-gradient-to-r from-destructive/10 to-destructive/5 rounded-md border border-destructive/20 mt-2">
          <div className="text-destructive text-sm">
            {t("error")}: {modelsError}
          </div>
        </div>
      ) : (
        <RichSelect value={selectedModel?.model_id || ""} onValueChange={onModelSelect}>
          <RichSelectTrigger size="lg" className="w-full max-w-full mt-2 bg-gradient-to-r from-background to-muted/30 border-border/50 hover:border-border rounded-xl">
            <RichSelectValue placeholder={t("select_model")}>
              {selectedModel && (
                <div className="flex items-center gap-2">
                  {convertModelToOption(selectedModel).icon}
                  <div className="flex flex-col items-start text-left gap-0.5 min-w-0 flex-1">
                    <span className="text-sm font-semibold truncate w-full">{selectedModel.model_name}</span>
                    <span className="text-xs font-medium text-muted-foreground truncate w-full">
                      {selectedModel.description || `${selectedModel.credits_per_unit} credits/${selectedModel.unit_type} `}
                    </span>
                  </div>
                </div>
              )}
            </RichSelectValue>
          </RichSelectTrigger>
          <RichSelectContent className="z-[150]">
            {getAllModelOptions().map((option) => (
              <RichSelectItem
                key={option.value}
                value={option.value}
                option={option}
              />
            ))}
          </RichSelectContent>
        </RichSelect>
      )}
    </div>
  );
}
